<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Frontend Authentication Test</h1>
    
    <div class="test-section">
        <h3>Test Admin Login</h3>
        <button onclick="testAdminLogin()">Test Admin Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Test Admin Profile Access</h3>
        <button onclick="testAdminProfile()">Test Admin Profile</button>
        <div id="profile-result"></div>
    </div>

    <div class="test-section">
        <h3>Test Admin Logout</h3>
        <button onclick="testAdminLogout()">Test Admin Logout</button>
        <div id="logout-result"></div>
    </div>

    <script>
        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>Testing admin login...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include', // Important for cookies
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Login Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Login Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testAdminProfile() {
            const resultDiv = document.getElementById('profile-result');
            resultDiv.innerHTML = '<p>Testing admin profile access...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/current', {
                    method: 'GET',
                    credentials: 'include' // Important for cookies
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Profile Access Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Profile Access Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Profile Access Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testAdminLogout() {
            const resultDiv = document.getElementById('logout-result');
            resultDiv.innerHTML = '<p>Testing admin logout...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/logout', {
                    method: 'POST',
                    credentials: 'include' // Important for cookies
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Logout Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Logout Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Logout Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
