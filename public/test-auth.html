<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>Frontend-Backend Authentication Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Health Check</h3>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Admin <PERSON>gin</h3>
        <button onclick="testLogin()">Test Admin Login</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Get Admin Profile</h3>
        <button onclick="testProfile()">Test Get Profile</button>
        <div id="profile-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>Success!</strong><br>
                    Status: ${response.status}<br>
                    Response: ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            try {
                const response = await fetch(`${API_BASE}/auth/admin/login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.innerHTML = `
                    <strong>${response.ok ? 'Success!' : 'Error!'}</strong><br>
                    Status: ${response.status}<br>
                    Response: ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function testProfile() {
            const resultDiv = document.getElementById('profile-result');
            try {
                const response = await fetch(`${API_BASE}/auth/admin/profile`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.innerHTML = `
                    <strong>${response.ok ? 'Success!' : 'Error!'}</strong><br>
                    Status: ${response.status}<br>
                    Response: ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
