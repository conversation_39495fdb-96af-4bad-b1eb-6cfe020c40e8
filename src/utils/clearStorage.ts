/**
 * Utility functions for clearing localStorage data
 * Used during authentication migration from localStorage to HTTP-only cookies
 */

export const clearAllLocalStorage = (): void => {
  try {
    localStorage.clear();
    console.log('All localStorage data cleared');
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
};

export const clearSelectedLocalStorage = (keys: string[]): void => {
  try {
    keys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Removed localStorage key: ${key}`);
    });
  } catch (error) {
    console.error('Error clearing selected localStorage keys:', error);
  }
};

export const clearAuthRelatedStorage = (): void => {
  const authKeys = [
    'token',
    'user',
    'admin',
    'authToken',
    'adminToken',
    'isAuthenticated',
    'userRole',
    'adminRole'
  ];
  
  clearSelectedLocalStorage(authKeys);
};

export const clearSessionStorage = (): void => {
  try {
    sessionStorage.clear();
    console.log('All sessionStorage data cleared');
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
  }
};
