import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography, Alert } from 'antd';
import authService from '../services/authService';

const { Title, Text } = Typography;

const DebugAuth: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testLogin = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('Environment variables:');
      console.log('REACT_APP_API_URL:', process.env.REACT_APP_API_URL);
      console.log('NODE_ENV:', process.env.NODE_ENV);
      
      console.log('Testing login...');
      const response = await authService.adminLogin('<EMAIL>', 'admin123');
      console.log('Login response:', response);
      
      setResult(`SUCCESS: ${JSON.stringify(response, null, 2)}`);
    } catch (error: any) {
      console.error('Login error:', error);
      setResult(`ERROR: ${error.message}\nFull error: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testProfile = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('Testing profile...');
      const response = await authService.getAdminProfile();
      console.log('Profile response:', response);
      
      setResult(`SUCCESS: ${JSON.stringify(response, null, 2)}`);
    } catch (error: any) {
      console.error('Profile error:', error);
      setResult(`ERROR: ${error.message}\nFull error: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card style={{ margin: '20px', maxWidth: '800px' }}>
      <Title level={3}>Authentication Debug</Title>
      
      <div style={{ marginBottom: '20px' }}>
        <Text strong>Environment:</Text>
        <ul>
          <li>REACT_APP_API_URL: {process.env.REACT_APP_API_URL || 'undefined'}</li>
          <li>NODE_ENV: {process.env.NODE_ENV}</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <Button 
          type="primary" 
          onClick={testLogin} 
          loading={loading}
          style={{ marginRight: '10px' }}
        >
          Test Login
        </Button>
        <Button 
          onClick={testProfile} 
          loading={loading}
        >
          Test Profile
        </Button>
      </div>

      {result && (
        <Alert
          message="Result"
          description={<pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>{result}</pre>}
          type={result.startsWith('SUCCESS') ? 'success' : 'error'}
          style={{ marginTop: '20px' }}
        />
      )}
    </Card>
  );
};

export default DebugAuth;
